import { ViewCameraRuler, ViewCameraRulerType, ViewCameraType } from "../ViewCameraRuler"
import { CategoryName } from "@/Apps/LayoutAI/Scene3D/NodeName"

// 全景视角规则配置
export const PanoramaViewCameraConfigs: ViewCameraRuler[] = [
    {
        name: '客厅-朝向',
        typeId: ViewCameraRulerType.SofaView,
        target: CategoryName.Sofa,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'sofa_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },



    {
        name: '卧室-朝向',
        typeId: ViewCameraRulerType.BedView,
        target: CategoryName.Bed,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'bed_view_camera',
            fov: 60,
        },
        pose: {
            posObj: CategoryName.Bed,
            z: 1200,
            norOffset: 200,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },

    {
        name: '卫生间-朝向',
        typeId: ViewCameraRulerType.BathroomPanoView,
        target: `${CategoryName.BathCabinet}|${CategoryName.Toilet}|${CategoryName.Shower}`,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'bathroom_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            roomName: '卫生间',
        },
    }
]