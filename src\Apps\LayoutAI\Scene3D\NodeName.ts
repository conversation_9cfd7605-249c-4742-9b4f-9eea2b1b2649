/**************************************************
 * 纯常量定义，不用 import 其它文件，避免文件依赖。*
 **************************************************/

import { Group } from "three";


/**
 * @description 开关配置
 * <AUTHOR>
 * @date 2025-05-16 10:28:13
 * @lastEditTime 2025-05-16 10:28:13
 * @lastEditors xuld
 */
export class SwitchConfig {
    // 灯带是否使用补光板
    public static readonly useFillLight = false;
    // 受光材质是否开启
    public static readonly lightMaterialSwitch: boolean = false;
    // 家具灯光
    public static readonly furnitureLightSwitch: boolean = false;
    // 阴影是否开启
    public static readonly shadowSwitch: boolean = false;
    // 是否显示夜光模式 (要求前端不响应，但渲染生效)
    public static showNightMode: boolean = false;
}
(globalThis as any).SwitchConfig = SwitchConfig;

/**
 * @description 根节点名称
 * <AUTHOR>
 * @date 2024-12-16 15:03:24
 * @lastEditTime 2024-12-16 15:03:24
 * @lastEditors xuld
 */
export class RootGroupName {
    public static readonly Root: string = "GroupRoot"; // 根节点层
    public static readonly Walls: string = "GroupWalls"; // 墙体层
    public static readonly Windows: string = "GroupWindows"; // 窗户层
    public static readonly Rooms: string = "GroupRooms"; // 房间层
    public static readonly Furniture: string = "GroupFurniture"; // 家具层
    public static readonly DecorationLights: string = "GroupDecorationLights"; // 装饰灯层
    public static readonly DayLights: string = "GroupDayLights"; // 日光层
    public static readonly NightLights: string = "GroupNightLights"; // 夜光层
    public static readonly AmbientLights: string = "GroupAmbientLights"; // 环境光层
    public static readonly TopViewLights: string = "GroupTopViewLights"; // 顶视图灯光层
    public static readonly AILightsRT: string = "GroupAILightsRT"; // AI灯光实时渲染层
    public static readonly AILightsOffline: string = "GroupAILightsOffline"; // AI灯光离线渲染层
    public static readonly AILightsTest: string = "GroupAILightsTest"; // AI灯光测试层
}

/**
 * @description 网格名称
 * <AUTHOR>
 * @date 2024-12-16 15:03:24
 * @lastEditTime 2024-12-16 15:03:24
 * @lastEditors xuld
 */
export class MeshName {
    public static readonly Box: string = "Box";
    public static readonly BoxSegment: string = "BoxSegment";
    public static readonly Poly: string = "Poly";
    public static readonly OutterWallFace = "OutterWallFace";
    public static readonly Wall: string = "Wall";
    public static readonly Sky: string = "Sky";
    public static readonly WinDoor: string = "WinDoorEntity";
    public static readonly Furniture: string = "Furniture";
    public static readonly BaseGroup: string = "BaseGroup";
    public static readonly FigureSample: string = "FigureSample";
    public static readonly BoardBox: string = "BoardBox";
    public static readonly Board: string = "Board";
    public static readonly SolidPart: string = "SolidPart";
    public static readonly Painter: string = "Painter";
    public static readonly PainterPart: string = "PainterPart";
    public static readonly Ceiling: string = "Ceiling";
    public static readonly CeilingLayer: string = "CeilingLayer";
    public static readonly DecorationGroup: string = "DecorationGroup";
    public static readonly LightSlot: string = "LightSlot";
    public static readonly Floor: string = "Floor";
    public static readonly InnerWall: string = "InnerWall";
    public static readonly TableTop: string = "TableTop";
    public static readonly Axis: string = "Axis";
    public static readonly TopArrow: string = "TopArrow";
    public static readonly TopPart: string = "TopPart";
    public static readonly Axes: string = "Axes";
    public static readonly ShadowPlane: string = "ShadowPlane";
    public static readonly ShadowWall: string = "ShadowWall";
    public static readonly Quad: string = "Quad";
    public static readonly SimpleLight: string = "SimpleLight";
    public static readonly WallLight: string = "WallLight";
    public static readonly Pass: string = "Pass";
    public static readonly SVJPart: string = "SVJPart";
    public static readonly LightModel: string = "LightModel";
    public static readonly Model: string = "Model";
    public static readonly CustomModel: string = "CustomModel";
    public static readonly GroupModel: string = "GroupModel";
    public static readonly SVJGroup: string = "SVJGroup";
    public static readonly SkirtBoard : string = "SkirtBoard";
    public static readonly SimpleMesh: string = "SimpleMesh";
    public static readonly WhiteBox: string = "WhiteBox";
    public static readonly BoxBoundary: string = "BoxBoundary";
    public static readonly SolidMaterial: string = "SolidMaterial";
    public static readonly Figure2D: string = "Figure2D";
}


/**
 * @description 自定义数据名称
 * <AUTHOR>
 * @date 2024-12-16 15:03:24
 * @lastEditTime 2024-12-16 15:03:24
 * @lastEditors xuld
 */
export class UserDataKey {
    public static readonly MaterialId: string = "MaterialId";
    public static readonly IsModel: string = "IsModel";
    public static readonly MaterialInfo: string = "MaterialInfo";
    public static readonly EntityOfMesh: string = "EntityOfMesh";
    public static readonly BorderLine: string = "BorderLine";
    public static readonly Renderer: string = "Renderer";
    public static readonly SkipOutline: string = "SkipOutline";
    public static readonly Light: string = "Light";
    public static readonly FurnitureType: string = "FurnitureType";
    public static readonly FigureMeshTypeName: string = "FigureMeshTypeName";
    public static readonly BoundingBox: string = "BoundingBox";
    public static readonly MainChildBBox: string = "MainChildBBox";
    public static readonly key_color_material = 'color_material'; // 纯色材质
    public static readonly key_standard_material = 'standard_material'; // 标准材质
    public static readonly key_white_material = 'white_material'; // 白模材质
    public static readonly key_normal_material = 'key_normal_material'; // 法向图材质
    public static readonly key_depth_material = 'key_depth_material'; // 深度图材质

    public static readonly key_texture_material_url = 'key_texture_material_url';

    /**
   *  Added by : shiwei
   *  代理对象---用于快速计算求交网格
   *    --- 绑定在 SolidMesh的UserData中, 不参与渲染，主要参与raycastControls
   */
    public static readonly ProxyBox = "ProxyBox";

    // 记录 svj 模型中的 IES 灯光数据
    public static readonly ModelLights: string = "ModelLights";
    // 记录 svj 模型中的 mesh 数据
    public static readonly MeshLight: string = "MeshLight";
}


/**
* @description 材质分类名字
* <AUTHOR>
* @date 2025-05-14 10:28:13
* @lastEditTime 2025-05-14 10:28:13
* @lastEditors xuld
*/
export class CategoryName {
    public static readonly LightStrip = "灯带";
    public static readonly DownLight = "筒灯";
    public static readonly Sofa = "沙发";
    public static readonly TVCabinet = "电视柜";
    public static readonly Table = "餐桌";
    public static readonly Bed = "床";
    public static readonly BathCabinet = "浴室柜";
    public static readonly Toilet = "马桶";
    public static readonly Shower = "淋浴房";
    public static readonly Teapoy = "茶几";
    public static readonly Desk = "书桌";
    public static readonly TeaTable = "茶台";
    public static readonly Curtain = "窗帘";
}


/**
 * @description 具体类型名称
 * <AUTHOR>
 * @date 2025-06-17 10:28:13
 * @lastEditTime 2025-06-17 10:28:13
 * @lastEditors xuld
 */
export class RealType {
    // 门洞
    public static readonly DoorHole = "DoorHole";
    // 栏杆
    public static readonly Railing = "Railing";
    // 推拉门
    public static readonly SlidingDoor = "SlidingDoor";
}


export class FurnitureObject3D extends Group
{
}
export class BaseGroupObject3D extends Group{

}

export class FigureElement3D extends Group {
    
}

export class SkirtBoardObject3D extends Group {
    
}