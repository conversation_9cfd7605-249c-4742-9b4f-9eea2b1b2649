import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { BaseViewHandler } from "./BaseViewHandler";
import { ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";

export class RoomTargetViewHandler extends BaseViewHandler {
    handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            let targetList = (ruler.target as string).trim().split("|");
            console.log("targetList", targetList);
            let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();

            let posObj: TFurnitureEntity = null;
            if (ruler.pose?.posObj) {
                posObj = furnitureList.find(furniture => furniture.category === ruler.pose.posObj);
            }
            
            // 若当前图元不存在则判断下一个
            for (let target of targetList) {
                for (let furniture of furnitureList) { 
                    if (furniture.category !== target) { 
                        continue; 
                    } 
                    let entity: TViewCameraEntity; 
                    // 是否按照分区布置
                    if (ruler.condition?.spaceArea) { 
                        let areasEntities = roomEntity._sub_room_areas as TSubSpaceAreaEntity[]; 
                        areasEntities.forEach(areaEntity => { 
                            if (IType2UITypeDict[areaEntity.space_area_type] === ruler.condition.spaceArea ) {
                                // 是否有位置对象
                                if (posObj) {
                                    entity = this.createTargetView(ruler, options, furniture, roomEntity,  posObj);
                                } else {
                                    entity = this.createTargetView(ruler, options, furniture, roomEntity,  areaEntity);
                                }
                            }
                        });
                    } else {
                        // 是否有位置对象
                        if (posObj) {
                            entity = this.createTargetView(ruler, options, furniture, roomEntity,  posObj);
                        } else {
                            entity = this.createTargetView(ruler, options, furniture, roomEntity,  roomEntity);
                        }
                    }
                    entities.push(entity);
                    break;
                }
            }
        }
        return entities;
    }

    private createTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        furniture: TFurnitureEntity,
        roomEntity: TRoomEntity,
        entity: TRoomEntity | TSubSpaceAreaEntity | TFurnitureEntity // 位置对象
    ): TViewCameraEntity {
        let pos = this.getViewCameraPos(ruler, entity);
        const furnitureRect = furniture.matched_rect || furniture.rect;
        let rect = new ZRect(500, 500);
        rect.nor = furnitureRect.nor.clone().negate();
        rect.rect_center_3d = pos;
        const target = [furniture.category]; 
        let viewEntity = this.createViewCameraEntity(ruler, options, rect, target, roomEntity); 
        return viewEntity; 
    }
}
